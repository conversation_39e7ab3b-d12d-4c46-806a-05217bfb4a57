import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei']
rcParams['axes.unicode_minus'] = False

def load_data():
    """加载供应商数据"""
    try:
        # 读取附件1数据
        df = pd.read_excel('2021C/附件1 近5年402家供应商的相关数据.xlsx')
        print("数据加载成功！")
        print(f"数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        return df
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None

def preprocess_data(df):
    """数据预处理"""
    print("\n=== 数据预处理 ===")
    
    # 查看数据基本信息
    print("数据基本信息:")
    print(df.info())
    print("\n前5行数据:")
    print(df.head())
    
    # 检查缺失值
    print(f"\n缺失值统计:")
    print(df.isnull().sum())
    
    return df

def calculate_supplier_metrics(df):
    """计算供应商评价指标"""
    print("\n=== 计算供应商评价指标 ===")
    
    # 假设数据列包含供应商ID、订货量、供货量等信息
    # 需要根据实际数据结构调整
    
    supplier_metrics = {}
    
    # 按供应商分组计算指标
    for supplier_id in df['供应商ID'].unique():
        supplier_data = df[df['供应商ID'] == supplier_id]
        
        # 计算各项指标
        total_order = supplier_data['订货量'].sum()  # 总订货量
        total_supply = supplier_data['供货量'].sum()  # 总供货量
        
        # 供货稳定性 - 供货量与订货量的比值标准差
        supply_ratio = supplier_data['供货量'] / supplier_data['订货量']
        stability = 1 / (1 + supply_ratio.std()) if supply_ratio.std() > 0 else 1
        
        # 供货能力 - 平均供货量
        capacity = supplier_data['供货量'].mean()
        
        # 可靠性 - 供货量/订货量的平均值
        reliability = (supplier_data['供货量'] / supplier_data['订货量']).mean()
        
        # 供货频次 - 供货次数
        frequency = len(supplier_data)
        
        supplier_metrics[supplier_id] = {
            'total_order': total_order,
            'total_supply': total_supply,
            'stability': stability,
            'capacity': capacity,
            'reliability': reliability,
            'frequency': frequency
        }
    
    return supplier_metrics

def build_evaluation_model(supplier_metrics):
    """建立供应商评价模型"""
    print("\n=== 建立供应商评价模型 ===")
    
    # 转换为DataFrame便于处理
    metrics_df = pd.DataFrame(supplier_metrics).T
    
    # 数据标准化
    from sklearn.preprocessing import StandardScaler
    scaler = StandardScaler()
    
    # 选择评价指标
    features = ['stability', 'capacity', 'reliability', 'frequency']
    X = metrics_df[features]
    X_scaled = scaler.fit_transform(X)
    
    # 设置权重（可根据实际情况调整）
    weights = {
        'stability': 0.3,    # 稳定性权重
        'capacity': 0.25,    # 供货能力权重  
        'reliability': 0.3,  # 可靠性权重
        'frequency': 0.15    # 供货频次权重
    }
    
    # 计算综合评分
    scores = []
    for i, supplier_id in enumerate(metrics_df.index):
        score = 0
        for j, feature in enumerate(features):
            score += weights[feature] * X_scaled[i, j]
        scores.append(score)
    
    metrics_df['综合评分'] = scores
    
    return metrics_df

def select_top_suppliers(metrics_df, top_n=50):
    """选择前50名重要供应商"""
    print(f"\n=== 选择前{top_n}名重要供应商 ===")
    
    # 按综合评分排序
    top_suppliers = metrics_df.sort_values('综合评分', ascending=False).head(top_n)
    
    # 重新排名
    top_suppliers['排名'] = range(1, top_n + 1)
    
    return top_suppliers

def create_result_table(top_suppliers):
    """创建结果表格"""
    print("\n=== 创建结果表格 ===")
    
    # 选择需要显示的列
    result_columns = ['排名', '综合评分', 'stability', 'capacity', 'reliability', 'frequency']
    result_table = top_suppliers[result_columns].copy()
    
    # 重命名列
    result_table.columns = ['排名', '综合评分', '稳定性', '供货能力', '可靠性', '供货频次']
    
    # 格式化数值
    for col in ['综合评分', '稳定性', '供货能力', '可靠性', '供货频次']:
        result_table[col] = result_table[col].round(4)
    
    return result_table

def main():
    """主函数"""
    print("=== 供应商评价选择分析 ===")
    
    # 1. 加载数据
    df = load_data()
    if df is None:
        return
    
    # 2. 数据预处理
    df = preprocess_data(df)
    
    # 3. 计算供应商指标
    supplier_metrics = calculate_supplier_metrics(df)
    
    # 4. 建立评价模型
    metrics_df = build_evaluation_model(supplier_metrics)
    
    # 5. 选择前50名供应商
    top_suppliers = select_top_suppliers(metrics_df, 50)
    
    # 6. 创建结果表格
    result_table = create_result_table(top_suppliers)
    
    # 7. 输出结果
    print("\n=== 前50名重要供应商 ===")
    print(result_table.to_string(index=True))
    
    # 8. 保存结果
    result_table.to_excel('前50名重要供应商.xlsx', index=True)
    print(f"\n结果已保存到: 前50名重要供应商.xlsx")
    
    return result_table

if __name__ == "__main__":
    result = main()
