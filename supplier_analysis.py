import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei']
rcParams['axes.unicode_minus'] = False

def load_data():
    """加载供应商数据"""
    try:
        # 读取附件1数据
        df = pd.read_excel('2021C/附件1 近5年402家供应商的相关数据.xlsx')
        print("数据加载成功！")
        print(f"数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        return df
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None

def preprocess_data(df):
    """数据预处理"""
    print("\n=== 数据预处理 ===")

    # 查看数据基本信息
    print("数据基本信息:")
    print(df.info())
    print("\n前5行数据:")
    print(df.head())

    # 检查缺失值
    print(f"\n缺失值统计:")
    print(df.isnull().sum())

    return df

def visualize_raw_data(df):
    """可视化原始数据"""
    print("\n=== 原始数据可视化 ===")

    # 获取周次列
    week_columns = [col for col in df.columns if col.startswith('W')]

    # 计算每个供应商的总供货量
    df['总供货量'] = df[week_columns].sum(axis=1)

    # 1. 供应商总供货量分布
    plt.figure(figsize=(15, 10))

    plt.subplot(2, 3, 1)
    plt.hist(df['总供货量'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    plt.xlabel('总供货量')
    plt.ylabel('供应商数量')
    plt.title('供应商总供货量分布')
    plt.grid(True, alpha=0.3)

    # 2. 材料分类分布
    plt.subplot(2, 3, 2)
    material_counts = df['材料分类'].value_counts()
    plt.pie(material_counts.values, labels=material_counts.index, autopct='%1.1f%%',
            colors=['lightcoral', 'lightblue', 'lightgreen'])
    plt.title('材料分类分布')

    # 3. 不同材料类型的供货量分布
    plt.subplot(2, 3, 3)
    for material in df['材料分类'].unique():
        material_data = df[df['材料分类'] == material]['总供货量']
        plt.hist(material_data, alpha=0.6, label=f'材料{material}', bins=30)
    plt.xlabel('总供货量')
    plt.ylabel('频次')
    plt.title('不同材料类型供货量分布')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 4. 供货量散点图（按材料分类着色）
    plt.subplot(2, 3, 4)
    colors = {'A': 'red', 'B': 'blue', 'C': 'green'}
    for material in df['材料分类'].unique():
        material_data = df[df['材料分类'] == material]
        plt.scatter(range(len(material_data)), material_data['总供货量'],
                   c=colors[material], label=f'材料{material}', alpha=0.6, s=20)
    plt.xlabel('供应商序号')
    plt.ylabel('总供货量')
    plt.title('供应商供货量分布（按材料分类）')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 5. 供货量大于100的供应商分布
    plt.subplot(2, 3, 5)
    filtered_df = df[df['总供货量'] > 100]
    plt.hist(filtered_df['总供货量'], bins=30, alpha=0.7, color='orange', edgecolor='black')
    plt.xlabel('总供货量')
    plt.ylabel('供应商数量')
    plt.title(f'供货量>100的供应商分布\n(共{len(filtered_df)}家)')
    plt.grid(True, alpha=0.3)

    # 6. 供货量统计信息
    plt.subplot(2, 3, 6)
    stats_data = [
        df['总供货量'].mean(),
        df['总供货量'].median(),
        df['总供货量'].std(),
        df['总供货量'].max(),
        df['总供货量'].min()
    ]
    stats_labels = ['均值', '中位数', '标准差', '最大值', '最小值']
    plt.bar(stats_labels, stats_data, color=['skyblue', 'lightgreen', 'lightcoral', 'gold', 'lightpink'])
    plt.title('供货量统计信息')
    plt.ylabel('数值')
    plt.xticks(rotation=45)

    plt.tight_layout()
    plt.savefig('原始数据分析.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 打印统计信息
    print(f"总供应商数量: {len(df)}")
    print(f"供货量>100的供应商数量: {len(filtered_df)}")
    print(f"材料分类统计: {material_counts.to_dict()}")
    print(f"供货量统计: 均值={df['总供货量'].mean():.2f}, 中位数={df['总供货量'].median():.2f}")

    return df

def calculate_supplier_metrics(df):
    """计算供应商评价指标"""
    print("\n=== 计算供应商评价指标 ===")

    # 获取周次列（W001到W240）
    week_columns = [col for col in df.columns if col.startswith('W')]
    print(f"发现{len(week_columns)}个周次列")

    supplier_metrics = {}

    # 按供应商分组计算指标
    for supplier_id in df['供应商ID'].unique():
        supplier_data = df[df['供应商ID'] == supplier_id].iloc[0]  # 每个供应商只有一行数据

        # 获取该供应商的周次供货数据
        weekly_supply = supplier_data[week_columns].values

        # 计算各项指标
        total_supply = weekly_supply.sum()  # 总供货量

        # 供货稳定性 - 使用变异系数的倒数
        non_zero_supply = weekly_supply[weekly_supply > 0]
        if len(non_zero_supply) > 1:
            cv = np.std(non_zero_supply) / np.mean(non_zero_supply) if np.mean(non_zero_supply) > 0 else 0
            stability = 1 / (1 + cv)
        else:
            stability = 0.5  # 供货次数太少，给予中等评分

        # 供货能力 - 平均供货量
        capacity = np.mean(weekly_supply)

        # 可靠性 - 非零供货周次的比例
        reliability = len(non_zero_supply) / len(weekly_supply)

        # 供货频次 - 供货次数
        frequency = len(non_zero_supply)

        supplier_metrics[supplier_id] = {
            'total_supply': total_supply,
            'stability': stability,
            'capacity': capacity,
            'reliability': reliability,
            'frequency': frequency,
            'material_type': supplier_data['材料分类']
        }

    return supplier_metrics

def build_evaluation_model(supplier_metrics):
    """建立供应商评价模型"""
    print("\n=== 建立供应商评价模型 ===")

    # 转换为DataFrame便于处理
    metrics_df = pd.DataFrame(supplier_metrics).T
    print(f"供应商数量: {len(metrics_df)}")

    # 选择评价指标
    features = ['stability', 'capacity', 'reliability', 'frequency']

    # 数据标准化 - 使用简单的最大最小标准化
    for feature in features:
        max_val = metrics_df[feature].max()
        min_val = metrics_df[feature].min()
        if max_val > min_val:
            metrics_df[f'{feature}_normalized'] = (metrics_df[feature] - min_val) / (max_val - min_val)
        else:
            metrics_df[f'{feature}_normalized'] = 0.5  # 如果所有值相同，给予中等评分

    # 设置权重（可根据实际情况调整）
    weights = {
        'stability': 0.3,    # 稳定性权重
        'capacity': 0.25,    # 供货能力权重
        'reliability': 0.3,  # 可靠性权重
        'frequency': 0.15    # 供货频次权重
    }

    # 计算综合评分
    metrics_df['综合评分'] = (
        weights['stability'] * metrics_df['stability_normalized'] +
        weights['capacity'] * metrics_df['capacity_normalized'] +
        weights['reliability'] * metrics_df['reliability_normalized'] +
        weights['frequency'] * metrics_df['frequency_normalized']
    )

    return metrics_df

def select_top_suppliers(metrics_df, top_n=50):
    """选择前50名重要供应商"""
    print(f"\n=== 选择前{top_n}名重要供应商 ===")
    
    # 按综合评分排序
    top_suppliers = metrics_df.sort_values('综合评分', ascending=False).head(top_n)
    
    # 重新排名
    top_suppliers['排名'] = range(1, top_n + 1)
    
    return top_suppliers

def create_result_table(top_suppliers):
    """创建结果表格"""
    print("\n=== 创建结果表格 ===")

    # 创建结果表格
    result_table = pd.DataFrame()
    result_table['排名'] = top_suppliers['排名']
    result_table['供应商ID'] = top_suppliers.index
    result_table['综合评分'] = top_suppliers['综合评分'].round(6)
    result_table['材料分类'] = top_suppliers['material_type']
    result_table['总供货量'] = top_suppliers['total_supply'].round(2)
    result_table['稳定性'] = top_suppliers['stability'].round(4)
    result_table['供货能力'] = top_suppliers['capacity'].round(4)
    result_table['可靠性'] = top_suppliers['reliability'].round(4)
    result_table['供货频次'] = top_suppliers['frequency'].astype(int)

    return result_table

def visualize_evaluation_results(metrics_df, top_suppliers):
    """可视化评价结果"""
    print("\n=== 评价结果可视化 ===")

    plt.figure(figsize=(16, 12))

    # 1. 综合评分分布
    plt.subplot(3, 3, 1)
    plt.hist(metrics_df['综合评分'], bins=30, alpha=0.7, color='lightblue', edgecolor='black')
    plt.xlabel('综合评分')
    plt.ylabel('供应商数量')
    plt.title('供应商综合评分分布')
    plt.grid(True, alpha=0.3)

    # 2. 前50名供应商评分
    plt.subplot(3, 3, 2)
    plt.plot(range(1, 51), top_suppliers['综合评分'], 'o-', color='red', markersize=4)
    plt.xlabel('排名')
    plt.ylabel('综合评分')
    plt.title('前50名供应商评分趋势')
    plt.grid(True, alpha=0.3)

    # 3. 各指标雷达图（前10名平均值）
    plt.subplot(3, 3, 3)
    top10 = top_suppliers.head(10)
    indicators = ['稳定性', '供货能力', '可靠性', '供货频次']
    values = [
        top10['stability_normalized'].mean(),
        top10['capacity_normalized'].mean(),
        top10['reliability_normalized'].mean(),
        top10['frequency_normalized'].mean()
    ]

    angles = np.linspace(0, 2*np.pi, len(indicators), endpoint=False).tolist()
    values += values[:1]  # 闭合
    angles += angles[:1]

    ax = plt.subplot(3, 3, 3, projection='polar')
    ax.plot(angles, values, 'o-', linewidth=2, color='blue')
    ax.fill(angles, values, alpha=0.25, color='blue')
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(indicators)
    ax.set_title('前10名供应商平均指标')

    # 4. 材料分类在前50名中的分布
    plt.subplot(3, 3, 4)
    material_counts = top_suppliers['material_type'].value_counts()
    plt.pie(material_counts.values, labels=[f'材料{x}' for x in material_counts.index],
            autopct='%1.1f%%', colors=['lightcoral', 'lightblue', 'lightgreen'])
    plt.title('前50名供应商材料分类分布')

    # 5. 稳定性vs供货能力散点图
    plt.subplot(3, 3, 5)
    colors = {'A': 'red', 'B': 'blue', 'C': 'green'}
    for material in top_suppliers['material_type'].unique():
        material_data = top_suppliers[top_suppliers['material_type'] == material]
        plt.scatter(material_data['stability'], material_data['capacity'],
                   c=colors[material], label=f'材料{material}', alpha=0.7, s=50)
    plt.xlabel('稳定性')
    plt.ylabel('供货能力')
    plt.title('前50名供应商稳定性vs供货能力')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 6. 可靠性vs供货频次散点图
    plt.subplot(3, 3, 6)
    for material in top_suppliers['material_type'].unique():
        material_data = top_suppliers[top_suppliers['material_type'] == material]
        plt.scatter(material_data['reliability'], material_data['frequency'],
                   c=colors[material], label=f'材料{material}', alpha=0.7, s=50)
    plt.xlabel('可靠性')
    plt.ylabel('供货频次')
    plt.title('前50名供应商可靠性vs供货频次')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 7. 各指标箱线图
    plt.subplot(3, 3, 7)
    indicators_data = [
        top_suppliers['stability'],
        top_suppliers['capacity_normalized'],
        top_suppliers['reliability'],
        top_suppliers['frequency_normalized']
    ]
    plt.boxplot(indicators_data, labels=['稳定性', '供货能力', '可靠性', '供货频次'])
    plt.title('前50名供应商各指标分布')
    plt.ylabel('指标值')
    plt.xticks(rotation=45)

    # 8. 总供货量vs综合评分
    plt.subplot(3, 3, 8)
    plt.scatter(top_suppliers['total_supply'], top_suppliers['综合评分'],
               alpha=0.7, c='purple', s=50)
    plt.xlabel('总供货量')
    plt.ylabel('综合评分')
    plt.title('总供货量vs综合评分关系')
    plt.grid(True, alpha=0.3)

    # 9. 前20名供应商对比
    plt.subplot(3, 3, 9)
    top20 = top_suppliers.head(20)
    x_pos = np.arange(len(top20))
    plt.bar(x_pos, top20['综合评分'], color='gold', alpha=0.7)
    plt.xlabel('排名')
    plt.ylabel('综合评分')
    plt.title('前20名供应商评分对比')
    plt.xticks(x_pos[::2], top20.index[::2], rotation=45)

    plt.tight_layout()
    plt.savefig('供应商评价结果分析.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 打印分析结果
    print(f"\n=== 评价结果统计 ===")
    print(f"前50名供应商平均评分: {top_suppliers['综合评分'].mean():.4f}")
    print(f"前10名供应商平均评分: {top_suppliers.head(10)['综合评分'].mean():.4f}")
    print(f"前50名中各材料类型数量: {material_counts.to_dict()}")
    print(f"最高评分: {top_suppliers['综合评分'].max():.6f} (供应商: {top_suppliers.index[0]})")
    print(f"最低评分: {top_suppliers['综合评分'].min():.6f} (供应商: {top_suppliers.index[-1]})")

    return plt

def main():
    """主函数"""
    print("=== 供应商评价选择分析 ===")

    # 1. 加载数据
    df = load_data()
    if df is None:
        return

    # 2. 数据预处理
    df = preprocess_data(df)

    # 3. 原始数据可视化
    df = visualize_raw_data(df)

    # 4. 计算供应商指标
    supplier_metrics = calculate_supplier_metrics(df)

    # 5. 建立评价模型
    metrics_df = build_evaluation_model(supplier_metrics)

    # 6. 选择前50名供应商
    top_suppliers = select_top_suppliers(metrics_df, 50)

    # 7. 评价结果可视化
    visualize_evaluation_results(metrics_df, top_suppliers)

    # 8. 创建结果表格
    result_table = create_result_table(top_suppliers)

    # 9. 输出结果
    print("\n=== 前50名重要供应商 ===")
    print(result_table.to_string(index=True))

    # 10. 保存结果
    result_table.to_excel('前50名重要供应商.xlsx', index=True)
    print(f"\n结果已保存到: 前50名重要供应商.xlsx")

    return result_table

if __name__ == "__main__":
    result = main()
