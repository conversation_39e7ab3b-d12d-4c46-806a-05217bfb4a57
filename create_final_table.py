import pandas as pd
import numpy as np

def create_final_ranking_table():
    """创建最终的供应商排名表格"""
    
    # 读取生成的Excel文件
    try:
        df = pd.read_excel('前50名重要供应商.xlsx', index_col=0)
        print("成功读取供应商数据")
    except:
        print("无法读取Excel文件，使用示例数据")
        return
    
    # 创建三列格式的表格
    result_table = []
    
    # 将50个供应商分成三列，每列约17行
    suppliers_per_col = 17
    
    for i in range(suppliers_per_col):
        row = {}
        
        # 第一列 (排名1-17)
        if i < len(df):
            row['排名1'] = df.iloc[i]['排名']
            row['供应商ID1'] = df.iloc[i]['供应商ID']
            row['评分1'] = f"{df.iloc[i]['综合评分']:.8f}"
        else:
            row['排名1'] = ''
            row['供应商ID1'] = ''
            row['评分1'] = ''
        
        # 第二列 (排名18-34)
        idx2 = i + suppliers_per_col
        if idx2 < len(df):
            row['排名2'] = df.iloc[idx2]['排名']
            row['供应商ID2'] = df.iloc[idx2]['供应商ID']
            row['评分2'] = f"{df.iloc[idx2]['综合评分']:.8f}"
        else:
            row['排名2'] = ''
            row['供应商ID2'] = ''
            row['评分2'] = ''
        
        # 第三列 (排名35-50)
        idx3 = i + 2 * suppliers_per_col
        if idx3 < len(df):
            row['排名3'] = df.iloc[idx3]['排名']
            row['供应商ID3'] = df.iloc[idx3]['供应商ID']
            row['评分3'] = f"{df.iloc[idx3]['综合评分']:.8f}"
        else:
            row['排名3'] = ''
            row['供应商ID3'] = ''
            row['评分3'] = ''
        
        result_table.append(row)
    
    # 转换为DataFrame
    final_df = pd.DataFrame(result_table)
    
    # 重命名列
    final_df.columns = ['排名', '供应商ID', '评分', '排名', '供应商ID', '评分', '排名', '供应商ID', '评分']
    
    # 保存为Excel
    final_df.to_excel('前50名重要供应商_三列格式.xlsx', index=False)
    
    # 打印表格
    print("\n=== 前50名重要供应商（三列格式）===")
    print(final_df.to_string(index=False))
    
    return final_df

def create_markdown_table():
    """创建Markdown格式的表格"""
    
    try:
        df = pd.read_excel('前50名重要供应商.xlsx', index_col=0)
    except:
        print("无法读取Excel文件")
        return
    
    # 创建Markdown表格
    markdown_content = """# 前50名重要供应商排名结果

## 表4 前50名的供应商

| 排名 | 供应商ID | 评分 | 排名 | 供应商ID | 评分 | 排名 | 供应商ID | 评分 |
|------|----------|------|------|----------|------|------|----------|------|
"""
    
    # 分三列显示
    suppliers_per_col = 17
    
    for i in range(suppliers_per_col):
        row_parts = []
        
        # 第一列
        if i < len(df):
            row_parts.extend([
                str(df.iloc[i]['排名']),
                df.iloc[i]['供应商ID'],
                f"{df.iloc[i]['综合评分']:.8f}"
            ])
        else:
            row_parts.extend(['', '', ''])
        
        # 第二列
        idx2 = i + suppliers_per_col
        if idx2 < len(df):
            row_parts.extend([
                str(df.iloc[idx2]['排名']),
                df.iloc[idx2]['供应商ID'],
                f"{df.iloc[idx2]['综合评分']:.8f}"
            ])
        else:
            row_parts.extend(['', '', ''])
        
        # 第三列
        idx3 = i + 2 * suppliers_per_col
        if idx3 < len(df):
            row_parts.extend([
                str(df.iloc[idx3]['排名']),
                df.iloc[idx3]['供应商ID'],
                f"{df.iloc[idx3]['综合评分']:.8f}"
            ])
        else:
            row_parts.extend(['', '', ''])
        
        markdown_content += "| " + " | ".join(row_parts) + " |\n"
    
    # 添加分析说明
    markdown_content += """
## 结果分析

根据上述分析，作出了部分排名在前的供应商的特征分析，结合上图中的数据，可以发现排名靠前的供应商大多有着不少于上述指标均值的表现，或者大部分指标均表现良好，这与题意认识相符。这些排名在前的供应商与企业合作频繁、供货量大、能满足供货的清洁数量；并且交易量大，条件数量的企业合作频繁，供货量大，存在供货的清洁数量等。

这些供应商在企业供应链中占据重要地位，除此以外，排名靠前的企业有的供货清洁性较大，存在供货清洁方面一些缺陷是企业的全部类别需求，对于保障企业生产经营至关重要的作用。因此，评价体系的合理性可见一斑。
"""
    
    # 保存Markdown文件
    with open('前50名重要供应商排名.md', 'w', encoding='utf-8') as f:
        f.write(markdown_content)
    
    print("Markdown表格已保存到: 前50名重要供应商排名.md")
    
    return markdown_content

if __name__ == "__main__":
    # 创建三列格式表格
    create_final_ranking_table()
    
    # 创建Markdown表格
    create_markdown_table()
