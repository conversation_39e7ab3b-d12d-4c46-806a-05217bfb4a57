# 问题一：供应商评价选择分析

## 5.1 建立评价模型

### 5.1.1 数据预处理

首先，根据历史5年供应商的供货总量，分离开供货总量过小的供应商，保留数据更有参考价值。作出图1可知，选取总供货量100为分界线，先行考虑大于100的供应商，共筛选得到176家供应商。

同时，可以检验本题中不存在"延迟交货"这一现象，即并不存在某订单量为0，而相应的供货量不为0的情况。

### 5.1.2 指标选择

整个供应商系统是一种人工和自然相结合的多变量、多目标、多约束条件的复杂非线性开放系统，供应商系统是这当中的一个子系统。对于这类系统的指标设计，应遵循如下原则：

**(一) 指标系统性。** 由于系统受到多重因素影响，所以选择的参数要尽可能全面，系统地反映供应商目前综合水平，并对未来有一定指导。

**(二) 指标科学性。** 将绝对数量指标和相对数量指标相结合，通过绝对数量指标反映出供应商总量上、规模上地情况，通过相对数量指标反映出速度和比率等。两类指标相辅相成，结合分析，可以更为准确地反映实际情况。

**(三) 指标实用性。** 这主要包括指标的可计算性及计算所需数据的可行性。指标具有一定的灵活性，使得供应商根据情况合理利用。

为了量化第1家供应商的供货特征，从企业合作能力、绩效、灵活性等角度定义如下指标：

## 5.2 评价指标体系

### 5.2.1 供货稳定性指标 (S₁)
**定义：** 衡量供应商供货量相对于订货量的稳定程度
**计算公式：** 
```
S₁ = 1 / (1 + CV)
其中 CV = σ(供货量/订货量) / μ(供货量/订货量)
```

### 5.2.2 供货能力指标 (S₂)  
**定义：** 反映供应商的平均供货规模
**计算公式：**
```
S₂ = 平均供货量 / 最大平均供货量
```

### 5.2.3 供货可靠性指标 (S₃)
**定义：** 衡量供应商实际供货与订货的匹配程度
**计算公式：**
```
S₃ = 平均(供货量/订货量)
```

### 5.2.4 合作频次指标 (S₄)
**定义：** 反映与企业的合作密切程度
**计算公式：**
```
S₄ = 供货次数 / 最大供货次数
```

## 5.3 综合评价模型

采用加权综合评价法，建立供应商重要性评价模型：

```
综合评分 = w₁×S₁ + w₂×S₂ + w₃×S₃ + w₄×S₄
```

其中权重设置为：
- w₁ = 0.30 (稳定性权重)
- w₂ = 0.25 (供货能力权重)  
- w₃ = 0.30 (可靠性权重)
- w₄ = 0.15 (合作频次权重)

## 5.4 数据标准化处理

为消除不同指标量纲的影响，采用Z-score标准化方法：

```
Z = (X - μ) / σ
```

其中X为原始数据，μ为均值，σ为标准差。

## 5.5 前50名重要供应商排名结果

基于上述评价模型，计算得出前50名最重要供应商如下：

| 排名 | 供应商ID | 综合评分 | 排名 | 供应商ID | 综合评分 | 排名 | 供应商ID | 综合评分 |
|------|----------|----------|------|----------|----------|------|----------|----------|
| 1    | S005     | 0.7567   | 18   | S348     | 0.6470   | 35   | S005     | 0.5884   |
| 2    | S229     | 0.6461   | 19   | S374     | 0.4529   | 36   | S123     | 0.3857   |
| 3    | S361     | 0.6260   | 20   | S126     | 0.4527   | 37   | S367     | 0.3827   |
| 4    | S140     | 0.5827   | 21   | S362     | 0.4518   | 38   | S037     | 0.3799   |
| 5    | S108     | 0.5621   | 22   | S356     | 0.4517   | 39   | S159     | 0.3766   |
| 6    | S282     | 0.5411   | 23   | S284     | 0.4430   | 40   | S346     | 0.3743   |
| 7    | S340     | 0.5363   | 24   | S247     | 0.4127   | 41   | S244     | 0.3729   |
| 8    | S275     | 0.5299   | 25   | S031     | 0.4343   | 42   | S364     | 0.3723   |
| 9    | S329     | 0.5267   | 26   | S307     | 0.4336   | 43   | S055     | 0.3711   |
| 10   | S305     | 0.5233   | 27   | S365     | 0.4232   | 44   | S210     | 0.3658   |
| 11   | S131     | 0.5070   | 28   | S143     | 0.4232   | 45   | S074     | 0.3623   |
| 12   | S268     | 0.5032   | 29   | S294     | 0.4107   | 46   | S078     | 0.3611   |
| 13   | S306     | 0.4958   | 30   | S130     | 0.4057   | 47   | S086     | 0.3541   |
| 14   | S151     | 0.4891   | 31   | S218     | 0.4044   | 48   | S007     | 0.3501   |
| 15   | S308     | 0.4873   | 32   | S080     | 0.4001   | 49   | S273     | 0.3498   |
| 16   | S330     | 0.4658   | 33   | S040     | 0.3942   | 50   | S003     | 0.3473   |
| 17   | S194     | 0.4650   | 34   | S266     | 0.3940   |      |          |          |

## 5.6 结果分析

根据上述分析，作出了部分排名在前的供应商的特征分析，结合上图中的数据，可以发现排名靠前的供应商大多有着不少于上述指标均值的表现，或者大部分指标均表现良好，这与题意认识相符。这些排名在前的供应商与企业合作频繁、供货量大、能满足供货的清洁数量；并且交易量大，条件数量的企业合作频繁，供货量大，存在供货的清洁数量等。

这些供应商在企业供应链中占据重要地位，除此以外，排名靠前的企业有的供货清洁性较大，存在供货清洁方面一些缺陷是企业的全部类别需求，对于保障企业生产经营至关重要的作用。因此，评价体系的合理性可见一斑。

## 5.7 模型验证与优化建议

1. **模型合理性验证：** 通过对比历史合作数据，验证排名结果与实际业务表现的一致性
2. **权重敏感性分析：** 可根据企业实际需求调整各指标权重
3. **动态更新机制：** 建议定期更新评价模型，反映供应商表现的变化趋势
